#include "zexuan/base/eventloop.hpp"
#include <thread>

thread_local EventLoop* t_loopInThisThread = nullptr;
EventLoop::EventLoop()
    : looping_(false),
      threadId_(std::this_thread::get_id()),
      logger_(logger::getFileLogger("eventloop"))
{
    if(t_loopInThisThread)
    {
        logger_->error("Another EventLoop exists in this thread");
    }
    else
    {
        t_loopInThisThread = this;
    }
}

EventLoop::~EventLoop()
{
    assert(!looping_);
    t_loopInThisThread = nullptr;
}

EventLoop::loop()
{
    assert(!looping_);
    assert(threadId_ == std::this_thread::get_id());
    looping_ = true;

    //epoll
    logger_->info("EventLoop stop looping");   
    looping_ = false;
}