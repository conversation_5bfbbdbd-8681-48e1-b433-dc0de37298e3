#include "noncopyable.hpp"
#include "logger.hpp"
#include <thread>
namespace zexuan{
namespace base{
class EventLoop : noncopyable{
public :
    EventLoop();
    ~EventLoop();

    void loop();
    void assertInLoopThread()
    {
        if(!isInLoopThread())
        {
            abortNotInLoopThread();
        }
    }
    bool isInLoopThread() const { return threadId_ == std::this_thread::get_id(); }
private:
    bool looping_;
    bool quit_;
    const std::thread::id threadId_;
    std::shared_ptr<spdlog::logger> logger_;
};
} // namespace base
} // namespace zexuan